#!/usr/bin/env python3
"""
IMPROVED AGGRESSIVE REFERENCE EXTRACTOR

This version fixes the hanging issue in pattern extraction by:
1. Using more efficient regex patterns
2. Adding timeouts and limits
3. Processing text in smaller chunks
4. Avoiding catastrophic backtracking
"""

import asyncio
import re
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from utils.logging_utils import get_logger

logger = get_logger(__name__)

@dataclass
class ExtractedReference:
    """Represents an extracted reference with metadata."""
    text: str
    number: Optional[int] = None
    authors: Optional[str] = None
    title: Optional[str] = None
    journal: Optional[str] = None
    year: Optional[str] = None
    doi: Optional[str] = None
    pmid: Optional[str] = None
    url: Optional[str] = None
    confidence: float = 0.0
    extraction_method: str = "unknown"
    source_section: str = "document"

class ImprovedAggressiveReferenceExtractor:
    """
    Improved aggressive reference extractor that doesn't hang.
    """
    
    def __init__(self):
        """Initialize the improved extractor."""
        self.max_text_length = 200000  # Limit text size to prevent hanging
        self.chunk_size = 10000  # Process text in chunks
        self.pattern_timeout = 30  # Max 30 seconds per pattern
        
        logger.info("🚀 Initialized IMPROVED Aggressive Reference Extractor")
    
    async def extract_references_from_text(self, text: str, filename: str = "") -> Dict[str, Any]:
        """
        Extract references from text using improved methods.
        """
        start_time = time.time()
        logger.info(f"🚀 Starting IMPROVED aggressive reference extraction for {filename}")
        logger.info(f"📄 Document length: {len(text):,} characters")
        
        # Limit text size to prevent hanging
        if len(text) > self.max_text_length:
            logger.warning(f"⚠️ Text too long ({len(text):,} chars), truncating to {self.max_text_length:,}")
            text = text[:self.max_text_length]
        
        try:
            # Strategy 1: Section-based extraction (fast)
            section_refs = await self._extract_from_reference_sections(text)
            logger.info(f"📚 Section-based extraction: {len(section_refs)} references")
            
            # Strategy 2: Numbered references (fast)
            numbered_refs = await self._extract_numbered_references(text)
            logger.info(f"🔢 Numbered extraction: {len(numbered_refs)} references")
            
            # Strategy 3: Author-year citations (fast)
            author_year_refs = await self._extract_author_year_citations(text)
            logger.info(f"👥 Author-year extraction: {len(author_year_refs)} references")
            
            # Strategy 4: Journal patterns (medium speed)
            journal_refs = await self._extract_journal_references(text)
            logger.info(f"📰 Journal extraction: {len(journal_refs)} references")
            
            # Strategy 5: Book references (fast)
            book_refs = await self._extract_book_references(text)
            logger.info(f"📖 Book extraction: {len(book_refs)} references")
            
            # Strategy 6: URLs and DOIs (fast)
            url_refs = await self._extract_urls_and_dois(text)
            logger.info(f"🔗 URL/DOI extraction: {len(url_refs)} references")
            
            # Strategy 7: IMPROVED pattern matching (safe and fast)
            pattern_refs = await self._extract_citation_patterns_safe(text)
            logger.info(f"🎯 Safe pattern extraction: {len(pattern_refs)} references")
            
            # Combine all references
            all_refs = (section_refs + numbered_refs + author_year_refs + 
                       journal_refs + book_refs + url_refs + pattern_refs)
            
            # Remove duplicates
            unique_refs = self._remove_duplicates(all_refs)
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(unique_refs, len(text))
            
            # Create detailed references for CSV
            detailed_refs = self._create_detailed_references(unique_refs)
            
            processing_time = time.time() - start_time
            logger.info(f"✅ Final result: {len(unique_refs)} unique references extracted in {processing_time:.1f}s")
            
            # Track extraction methods
            extraction_methods = {}
            for ref in unique_refs:
                method = ref.extraction_method
                extraction_methods[method] = extraction_methods.get(method, 0) + 1
            
            return {
                'references': [ref.text for ref in unique_refs],
                'detailed_references': detailed_refs,
                'total_found': len(unique_refs),
                'confidence_score': confidence_score,
                'extraction_methods': extraction_methods,
                'processing_time': processing_time,
                'filename': filename,
                'text_length': len(text)
            }
            
        except Exception as e:
            logger.error(f"❌ Error in improved aggressive extraction: {e}")
            return {
                'references': [],
                'detailed_references': [],
                'total_found': 0,
                'confidence_score': 0.0,
                'extraction_methods': {},
                'processing_time': time.time() - start_time,
                'filename': filename,
                'error': str(e)
            }
    
    async def _extract_citation_patterns_safe(self, text: str) -> List[ExtractedReference]:
        """
        SAFE pattern extraction that won't hang.
        Uses simple, efficient patterns and processes text in chunks.
        """
        references = []
        
        # Process text in smaller chunks to avoid hanging
        chunks = [text[i:i+self.chunk_size] for i in range(0, len(text), self.chunk_size)]
        
        # Simple, safe patterns that won't cause backtracking
        safe_patterns = [
            # Simple author-year pattern
            r'\b[A-Z][a-z]+\s+\(\d{4}\)',
            
            # Simple journal pattern
            r'\b(?:Journal|Proc|Ann|Clin)\s+[A-Z][a-z]+\s+\d{4}',
            
            # Simple DOI pattern
            r'doi:\s*10\.\d+/[^\s]+',
            
            # Simple URL pattern
            r'https?://[^\s]+',
            
            # Simple et al pattern
            r'\b[A-Z][a-z]+\s+et\s+al\.?\s+\d{4}',
        ]
        
        for i, chunk in enumerate(chunks):
            if i > 20:  # Limit to first 20 chunks to prevent hanging
                logger.warning("⚠️ Limiting pattern extraction to first 20 chunks")
                break
                
            for pattern in safe_patterns:
                try:
                    # Use timeout for each pattern
                    matches = await asyncio.wait_for(
                        self._find_pattern_matches(pattern, chunk),
                        timeout=5.0  # 5 second timeout per pattern
                    )
                    
                    for match_text in matches:
                        if len(match_text) > 20:  # Minimum length
                            ref = ExtractedReference(
                                text=match_text,
                                confidence=0.4,  # Lower confidence for pattern matches
                                extraction_method="safe_pattern",
                                source_section="document_body"
                            )
                            references.append(ref)
                            
                except asyncio.TimeoutError:
                    logger.warning(f"⏰ Pattern timeout for: {pattern[:30]}...")
                    continue
                except Exception as e:
                    logger.warning(f"⚠️ Pattern error: {e}")
                    continue
        
        return references[:100]  # Limit to 100 pattern matches
    
    async def _find_pattern_matches(self, pattern: str, text: str) -> List[str]:
        """Find pattern matches safely."""
        try:
            matches = re.findall(pattern, text, re.IGNORECASE)
            return matches[:20]  # Limit matches per pattern
        except Exception as e:
            logger.warning(f"⚠️ Regex error: {e}")
            return []
    
    async def _extract_from_reference_sections(self, text: str) -> List[ExtractedReference]:
        """Extract references from dedicated reference sections."""
        references = []
        
        # Look for reference sections
        ref_section_patterns = [
            r'(?i)references?\s*\n(.*?)(?:\n\s*\n|\Z)',
            r'(?i)bibliography\s*\n(.*?)(?:\n\s*\n|\Z)',
            r'(?i)literature\s+cited\s*\n(.*?)(?:\n\s*\n|\Z)',
        ]
        
        for pattern in ref_section_patterns:
            try:
                matches = re.findall(pattern, text, re.DOTALL)
                for match in matches:
                    # Split into individual references
                    ref_lines = [line.strip() for line in match.split('\n') if line.strip()]
                    
                    for line in ref_lines[:50]:  # Limit to 50 references per section
                        if len(line) > 30:  # Minimum length for a reference
                            ref = ExtractedReference(
                                text=line,
                                confidence=0.8,
                                extraction_method="reference_section",
                                source_section="references"
                            )
                            references.append(ref)
            except Exception as e:
                logger.warning(f"⚠️ Reference section extraction error: {e}")
                continue
        
        return references
    
    async def _extract_numbered_references(self, text: str) -> List[ExtractedReference]:
        """Extract numbered references."""
        references = []
        
        # Simple numbered patterns
        patterns = [
            r'^\s*(\d+)\.\s+(.+)$',
            r'^\s*\[(\d+)\]\s+(.+)$',
            r'^\s*\((\d+)\)\s+(.+)$',
        ]
        
        lines = text.split('\n')
        for line in lines[:1000]:  # Limit to first 1000 lines
            for pattern in patterns:
                try:
                    match = re.match(pattern, line.strip())
                    if match and len(match.group(2)) > 20:
                        ref = ExtractedReference(
                            text=match.group(2).strip(),
                            number=int(match.group(1)),
                            confidence=0.7,
                            extraction_method="numbered",
                            source_section="document_body"
                        )
                        references.append(ref)
                        break
                except Exception:
                    continue
        
        return references
    
    async def _extract_author_year_citations(self, text: str) -> List[ExtractedReference]:
        """Extract author-year style citations."""
        references = []
        
        # Simple author-year patterns
        patterns = [
            r'\b([A-Z][a-z]+)\s+\((\d{4})\)',
            r'\b([A-Z][a-z]+)\s+et\s+al\.?\s+\((\d{4})\)',
            r'\(([A-Z][a-z]+),?\s+(\d{4})\)',
        ]
        
        for pattern in patterns:
            try:
                matches = re.findall(pattern, text)
                for match in matches[:50]:  # Limit matches
                    if len(match) >= 2:
                        ref_text = f"{match[0]} ({match[1]})"
                        ref = ExtractedReference(
                            text=ref_text,
                            authors=match[0],
                            year=match[1],
                            confidence=0.6,
                            extraction_method="author_year",
                            source_section="document_body"
                        )
                        references.append(ref)
            except Exception:
                continue
        
        return references
    
    async def _extract_journal_references(self, text: str) -> List[ExtractedReference]:
        """Extract journal-style references."""
        references = []
        
        # Simple journal patterns
        journal_keywords = ['Journal', 'Proc', 'Ann', 'Clin', 'Med', 'Sci', 'Res', 'Rev']
        
        for keyword in journal_keywords:
            try:
                pattern = rf'\b{keyword}\s+[A-Z][a-z]+[^.]*\d{{4}}[^.]*\.'
                matches = re.findall(pattern, text)
                
                for match in matches[:20]:  # Limit matches per keyword
                    if len(match) > 30:
                        ref = ExtractedReference(
                            text=match.strip(),
                            confidence=0.5,
                            extraction_method="journal_pattern",
                            source_section="document_body"
                        )
                        references.append(ref)
            except Exception:
                continue
        
        return references
    
    async def _extract_book_references(self, text: str) -> List[ExtractedReference]:
        """Extract book references."""
        references = []
        
        # Simple book patterns
        book_patterns = [
            r'\b[A-Z][a-z]+[^.]*\bPress\b[^.]*\d{4}[^.]*\.',
            r'\b[A-Z][a-z]+[^.]*\bPublisher\b[^.]*\d{4}[^.]*\.',
            r'\b[A-Z][a-z]+[^.]*\bEdition\b[^.]*\d{4}[^.]*\.',
        ]
        
        for pattern in book_patterns:
            try:
                matches = re.findall(pattern, text)
                for match in matches[:10]:  # Limit matches
                    if len(match) > 30:
                        ref = ExtractedReference(
                            text=match.strip(),
                            confidence=0.6,
                            extraction_method="book_pattern",
                            source_section="document_body"
                        )
                        references.append(ref)
            except Exception:
                continue
        
        return references
    
    async def _extract_urls_and_dois(self, text: str) -> List[ExtractedReference]:
        """Extract URLs and DOIs."""
        references = []
        
        # URL and DOI patterns
        patterns = [
            (r'https?://[^\s]+', 'url'),
            (r'doi:\s*10\.\d+/[^\s]+', 'doi'),
            (r'www\.[^\s]+', 'url'),
        ]
        
        for pattern, ref_type in patterns:
            try:
                matches = re.findall(pattern, text, re.IGNORECASE)
                for match in matches[:50]:  # Limit matches
                    ref = ExtractedReference(
                        text=match.strip(),
                        url=match.strip() if ref_type == 'url' else None,
                        doi=match.strip() if ref_type == 'doi' else None,
                        confidence=0.9,
                        extraction_method=f"{ref_type}_extraction",
                        source_section="document_body"
                    )
                    references.append(ref)
            except Exception:
                continue
        
        return references
    
    def _remove_duplicates(self, references: List[ExtractedReference]) -> List[ExtractedReference]:
        """Remove duplicate references."""
        seen = set()
        unique_refs = []
        
        for ref in references:
            # Create a normalized version for comparison
            normalized = re.sub(r'\s+', ' ', ref.text.lower().strip())
            
            if normalized not in seen and len(normalized) > 10:
                seen.add(normalized)
                unique_refs.append(ref)
        
        return unique_refs
    
    def _calculate_confidence_score(self, references: List[ExtractedReference], text_length: int) -> float:
        """Calculate overall confidence score."""
        if not references:
            return 0.0
        
        # Base confidence on average of individual confidences
        avg_confidence = sum(ref.confidence for ref in references) / len(references)
        
        # Adjust based on number of references found
        ref_count_factor = min(len(references) / 50, 1.0)  # Max factor at 50 refs
        
        # Adjust based on text length
        length_factor = min(text_length / 50000, 1.0)  # Max factor at 50k chars
        
        final_confidence = avg_confidence * ref_count_factor * length_factor
        return min(final_confidence, 1.0)
    
    def _create_detailed_references(self, references: List[ExtractedReference]) -> List[Dict[str, Any]]:
        """Create detailed reference dictionaries for CSV export."""
        detailed = []
        
        for i, ref in enumerate(references, 1):
            detailed.append({
                'number': ref.number or i,
                'text': ref.text,
                'authors': ref.authors or '',
                'title': ref.title or '',
                'journal': ref.journal or '',
                'year': ref.year or '',
                'doi': ref.doi or '',
                'pmid': ref.pmid or '',
                'url': ref.url or '',
                'confidence': ref.confidence,
                'extraction_method': ref.extraction_method,
                'source_section': ref.source_section
            })
        
        return detailed

# Global instance
_improved_extractor = None

async def get_improved_aggressive_reference_extractor() -> ImprovedAggressiveReferenceExtractor:
    """Get the global improved extractor instance."""
    global _improved_extractor
    if _improved_extractor is None:
        _improved_extractor = ImprovedAggressiveReferenceExtractor()
    return _improved_extractor
