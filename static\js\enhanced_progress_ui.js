/**
 * Enhanced Progress UI for Document Processing
 * 
 * This module provides a comprehensive, modern UI for tracking document processing
 * with detailed step-by-step progress, real-time statistics, and beautiful visualizations.
 */

class EnhancedProgressUI {
    constructor() {
        this.activeOperations = new Map();
        this.websockets = new Map();
        this.progressContainer = null;
        this.initializeContainer();
    }

    initializeContainer() {
        // Find or create the progress container
        this.progressContainer = document.getElementById('enhanced-progress-container');
        if (!this.progressContainer) {
            this.progressContainer = document.createElement('div');
            this.progressContainer.id = 'enhanced-progress-container';
            this.progressContainer.className = 'container-fluid mt-4';
            
            // Find a good place to insert it (after upload form)
            const uploadForm = document.querySelector('.upload-form, .card');
            if (uploadForm && uploadForm.parentNode) {
                uploadForm.parentNode.insertBefore(this.progressContainer, uploadForm.nextSibling);
            } else {
                document.body.appendChild(this.progressContainer);
            }
        }
    }

    startTracking(operationId, filename) {
        console.log(`🚀 Enhanced Progress UI: Starting tracking for ${filename} (${operationId})`);
        console.log(`📊 Progress container:`, this.progressContainer);

        // Create progress card
        const progressCard = this.createProgressCard(operationId, filename);
        console.log(`📊 Created progress card:`, progressCard);

        this.progressContainer.appendChild(progressCard);
        console.log(`📊 Added progress card to container`);

        // Start WebSocket connection
        this.connectWebSocket(operationId, filename);

        // Store operation info
        this.activeOperations.set(operationId, {
            filename: filename,
            startTime: new Date(),
            card: progressCard
        });

        console.log(`📊 Enhanced Progress UI: Tracking started for ${operationId}`);
    }

    createProgressCard(operationId, filename) {
        const card = document.createElement('div');
        card.className = 'card shadow-lg border-0 mb-4';
        card.id = `enhanced-progress-${operationId}`;
        card.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        card.style.color = 'white';
        
        card.innerHTML = `
            <div class="card-header border-0 d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text fs-4 me-3"></i>
                    <div>
                        <h5 class="mb-0 fw-bold">${filename}</h5>
                        <small class="opacity-75" id="operation-id-${operationId}">ID: ${operationId.substring(0, 8)}...</small>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <span class="badge bg-light text-dark me-3 fs-6" id="step-indicator-${operationId}">
                        Step 0/8
                    </span>
                    <span class="badge bg-warning text-dark me-3" id="eta-${operationId}">
                        Calculating...
                    </span>
                    <button class="btn btn-outline-light btn-sm me-2" onclick="enhancedProgressUI.stopOperation('${operationId}')"
                            id="stop-btn-${operationId}" title="Stop Processing">
                        <i class="bi bi-stop-circle"></i>
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="enhancedProgressUI.clearOperation('${operationId}')"
                            id="clear-btn-${operationId}" title="Clear">
                        <i class="bi bi-x-circle"></i>
                    </button>
                </div>
            </div>
            
            <div class="card-body">
                <!-- Overall Progress Bar -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="fw-bold">Overall Progress</span>
                        <span id="overall-percentage-${operationId}">0%</span>
                    </div>
                    <div class="progress" style="height: 12px; background: rgba(255,255,255,0.2);">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             id="overall-progress-${operationId}"
                             role="progressbar" style="width: 0%; background: linear-gradient(90deg, #28a745, #20c997);">
                        </div>
                    </div>
                    <div class="mt-2">
                        <small id="current-message-${operationId}" class="opacity-75">Initializing...</small>
                    </div>
                </div>

                <!-- Processing Steps -->
                <div class="row">
                    <div class="col-md-8">
                        <h6 class="fw-bold mb-3"><i class="bi bi-list-check me-2"></i>Processing Steps</h6>
                        <div id="steps-container-${operationId}" class="steps-container">
                            ${this.createStepsHTML(operationId)}
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <h6 class="fw-bold mb-3"><i class="bi bi-graph-up me-2"></i>Statistics</h6>
                        <div id="stats-container-${operationId}" class="stats-container">
                            ${this.createStatsHTML(operationId)}
                        </div>
                    </div>
                </div>

                <!-- Processing Log -->
                <div class="mt-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="fw-bold mb-0"><i class="bi bi-terminal me-2"></i>Processing Log</h6>
                        <button class="btn btn-outline-light btn-sm" onclick="enhancedProgressUI.toggleLog('${operationId}')">
                            <i class="bi bi-chevron-down" id="log-toggle-${operationId}"></i>
                        </button>
                    </div>
                    <div id="log-container-${operationId}" class="log-container" style="display: none; max-height: 200px; overflow-y: auto; background: rgba(0,0,0,0.2); border-radius: 8px; padding: 12px;">
                        <div id="log-content-${operationId}" class="font-monospace small">
                            <div class="log-entry">
                                <span class="text-info">[${new Date().toLocaleTimeString()}]</span> 
                                Processing started for ${filename}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        return card;
    }

    createStepsHTML(operationId) {
        const steps = [
            { key: 'initializing', name: 'Initializing', icon: 'bi-gear' },
            { key: 'text_extraction', name: 'Text Extraction', icon: 'bi-file-text' },
            { key: 'reference_extraction', name: 'Reference Extraction', icon: 'bi-bookmark' },
            { key: 'entity_extraction', name: 'Entity Extraction', icon: 'bi-tags' },
            { key: 'chunk_processing', name: 'Chunk Processing', icon: 'bi-puzzle' },
            { key: 'embedding_generation', name: 'Embedding Generation', icon: 'bi-vector-pen' },
            { key: 'metadata_extraction', name: 'Metadata Extraction', icon: 'bi-info-circle' },
            { key: 'finalization', name: 'Finalization', icon: 'bi-check-circle' }
        ];

        return steps.map(step => `
            <div class="step-item d-flex align-items-center mb-2 p-2 rounded" 
                 id="step-${step.key}-${operationId}" 
                 style="background: rgba(255,255,255,0.1);">
                <div class="step-icon me-3">
                    <i class="${step.icon} step-icon-${operationId}" id="icon-${step.key}-${operationId}"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold">${step.name}</div>
                    <small class="opacity-75" id="step-message-${step.key}-${operationId}">Pending...</small>
                </div>
                <div class="step-status">
                    <span class="badge bg-secondary" id="step-status-${step.key}-${operationId}">Pending</span>
                </div>
            </div>
        `).join('');
    }

    createStatsHTML(operationId) {
        return `
            <div class="stats-grid">
                <div class="stat-item mb-3 p-2 rounded" style="background: rgba(255,255,255,0.1);">
                    <div class="d-flex justify-content-between">
                        <span><i class="bi bi-file-text me-2"></i>Characters</span>
                        <span class="fw-bold" id="stat-characters-${operationId}">0</span>
                    </div>
                </div>
                <div class="stat-item mb-3 p-2 rounded" style="background: rgba(255,255,255,0.1);">
                    <div class="d-flex justify-content-between">
                        <span><i class="bi bi-puzzle me-2"></i>Chunks</span>
                        <span class="fw-bold" id="stat-chunks-${operationId}">0</span>
                    </div>
                </div>
                <div class="stat-item mb-3 p-2 rounded" style="background: rgba(255,255,255,0.1);">
                    <div class="d-flex justify-content-between">
                        <span><i class="bi bi-tags me-2"></i>Entities</span>
                        <span class="fw-bold" id="stat-entities-${operationId}">0</span>
                    </div>
                </div>
                <div class="stat-item mb-3 p-2 rounded" style="background: rgba(255,255,255,0.1);">
                    <div class="d-flex justify-content-between">
                        <span><i class="bi bi-bookmark me-2"></i>References</span>
                        <span class="fw-bold" id="stat-references-${operationId}">0</span>
                    </div>
                </div>
                <div class="stat-item mb-3 p-2 rounded" style="background: rgba(255,255,255,0.1);">
                    <div class="d-flex justify-content-between">
                        <span><i class="bi bi-vector-pen me-2"></i>Embeddings</span>
                        <span class="fw-bold" id="stat-embeddings-${operationId}">0</span>
                    </div>
                </div>
            </div>
        `;
    }

    connectWebSocket(operationId, filename) {
        const wsUrl = `ws://${window.location.host}/ws/progress/${operationId}`;
        const websocket = new WebSocket(wsUrl);
        
        websocket.onopen = () => {
            console.log(`🔌 WebSocket connected for ${filename} (${operationId})`);
            this.websockets.set(operationId, websocket);
            this.addLogEntry(operationId, 'WebSocket connection established', 'success');
        };
        
        websocket.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                this.handleWebSocketMessage(operationId, message);
            } catch (error) {
                console.error('Failed to parse WebSocket message:', error);
            }
        };
        
        websocket.onclose = () => {
            console.log(`🔌 WebSocket disconnected for ${filename} (${operationId})`);
            this.websockets.delete(operationId);
        };
        
        websocket.onerror = (error) => {
            console.error(`WebSocket error for ${filename}:`, error);
            this.addLogEntry(operationId, 'WebSocket connection error', 'error');
        };
    }

    handleWebSocketMessage(operationId, message) {
        console.log(`📨 WebSocket message for ${operationId}:`, message.type);
        console.log(`📊 Message data:`, message.data);

        switch (message.type) {
            case 'connection_established':
                this.addLogEntry(operationId, 'Connection established', 'info');
                break;

            case 'progress_update':
                console.log(`🔄 Processing progress update:`, message.data);
                this.updateProgress(operationId, message.data);
                break;

            case 'operation_complete':
                console.log(`✅ Operation complete:`, message.data);
                this.handleCompletion(operationId, message.data);
                break;

            case 'operation_error':
                console.log(`❌ Operation error:`, message.error);
                this.handleError(operationId, message.error, message.details);
                break;

            default:
                console.log(`❓ Unknown message type: ${message.type}`, message);
        }
    }

    updateProgress(operationId, data) {
        // Update overall progress
        const progressBar = document.getElementById(`overall-progress-${operationId}`);
        const progressText = document.getElementById(`overall-percentage-${operationId}`);
        const currentMessage = document.getElementById(`current-message-${operationId}`);
        const stepIndicator = document.getElementById(`step-indicator-${operationId}`);
        const etaElement = document.getElementById(`eta-${operationId}`);

        if (progressBar) {
            progressBar.style.width = `${data.overall_progress}%`;
            progressBar.setAttribute('aria-valuenow', data.overall_progress);
        }

        if (progressText) {
            progressText.textContent = `${data.overall_progress}%`;
        }

        if (currentMessage) {
            currentMessage.textContent = data.current_message;
        }

        if (stepIndicator) {
            stepIndicator.textContent = `Step ${data.current_step}/${data.total_steps}`;
        }

        // Update ETA
        if (etaElement && data.eta_seconds) {
            const eta = this.formatDuration(data.eta_seconds);
            etaElement.textContent = `ETA: ${eta}`;
            etaElement.className = 'badge bg-info text-dark me-3';
        }

        // Update steps
        this.updateSteps(operationId, data.steps);

        // Update statistics
        this.updateStatistics(operationId, data.statistics);

        // Add log entry
        this.addLogEntry(operationId, `${data.current_step_name}: ${data.current_message}`, 'info');
    }

    updateSteps(operationId, steps) {
        for (const [stepKey, stepData] of Object.entries(steps)) {
            const stepElement = document.getElementById(`step-${stepKey}-${operationId}`);
            const iconElement = document.getElementById(`icon-${stepKey}-${operationId}`);
            const messageElement = document.getElementById(`step-message-${stepKey}-${operationId}`);
            const statusElement = document.getElementById(`step-status-${stepKey}-${operationId}`);

            if (!stepElement) continue;

            // Update step appearance based on status
            stepElement.style.background = this.getStepBackgroundColor(stepData.status);

            // Update icon
            if (iconElement) {
                iconElement.className = this.getStepIcon(stepKey, stepData.status);
            }

            // Update message
            if (messageElement) {
                messageElement.textContent = stepData.message || this.getDefaultStepMessage(stepData.status);
            }

            // Update status badge
            if (statusElement) {
                statusElement.textContent = this.formatStepStatus(stepData.status);
                statusElement.className = `badge ${this.getStepBadgeClass(stepData.status)}`;
            }
        }
    }

    updateStatistics(operationId, statistics) {
        if (!statistics) return;

        const updates = [
            { id: `stat-characters-${operationId}`, value: statistics.total_characters?.toLocaleString() || '0' },
            { id: `stat-chunks-${operationId}`, value: statistics.total_chunks || '0' },
            { id: `stat-entities-${operationId}`, value: statistics.total_entities || '0' },
            { id: `stat-references-${operationId}`, value: statistics.total_references || '0' },
            { id: `stat-embeddings-${operationId}`, value: statistics.total_embeddings || '0' }
        ];

        updates.forEach(update => {
            const element = document.getElementById(update.id);
            if (element) {
                element.textContent = update.value;

                // Add animation for value changes
                element.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 200);
            }
        });
    }

    handleCompletion(operationId, data) {
        console.log(`🎉 Processing completed for ${operationId}`);

        // Update card appearance for completion
        const card = document.getElementById(`enhanced-progress-${operationId}`);
        if (card) {
            card.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
        }

        // Update progress to 100%
        const progressBar = document.getElementById(`overall-progress-${operationId}`);
        const progressText = document.getElementById(`overall-percentage-${operationId}`);
        const currentMessage = document.getElementById(`current-message-${operationId}`);
        const etaElement = document.getElementById(`eta-${operationId}`);
        const cancelBtn = document.getElementById(`cancel-btn-${operationId}`);

        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.className = 'progress-bar';
        }

        if (progressText) {
            progressText.textContent = '100%';
        }

        if (currentMessage) {
            currentMessage.textContent = data.current_message || 'Processing completed successfully!';
        }

        if (etaElement) {
            const duration = this.formatDuration(data.total_duration);
            etaElement.textContent = `Completed in ${duration}`;
            etaElement.className = 'badge bg-success me-3';
        }

        if (cancelBtn) {
            cancelBtn.style.display = 'none';
        }

        // Add completion log entry with summary
        this.addLogEntry(operationId, '🎉 Processing completed successfully!', 'success');
        this.addCompletionSummary(operationId, data);

        // Don't auto-hide - let user manually clear
        // User can use Clear button to remove completed operations
        console.log(`✅ Operation ${operationId} completed - use Clear button to remove`);
    }

    handleError(operationId, error, details) {
        console.error(`❌ Processing failed for ${operationId}:`, error);

        // Update card appearance for error
        const card = document.getElementById(`enhanced-progress-${operationId}`);
        if (card) {
            card.style.background = 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';
        }

        // Update UI elements
        const currentMessage = document.getElementById(`current-message-${operationId}`);
        const etaElement = document.getElementById(`eta-${operationId}`);
        const cancelBtn = document.getElementById(`cancel-btn-${operationId}`);

        if (currentMessage) {
            currentMessage.textContent = `Error: ${error}`;
        }

        if (etaElement) {
            etaElement.textContent = 'Failed';
            etaElement.className = 'badge bg-danger me-3';
        }

        if (cancelBtn) {
            cancelBtn.innerHTML = '<i class="bi bi-x-circle"></i> Close';
            cancelBtn.onclick = () => this.hideOperation(operationId);
        }

        // Add error log entry
        this.addLogEntry(operationId, `❌ Error: ${error}`, 'error');
        if (details) {
            this.addLogEntry(operationId, `Details: ${JSON.stringify(details)}`, 'error');
        }
    }

    addLogEntry(operationId, message, type = 'info') {
        const logContent = document.getElementById(`log-content-${operationId}`);
        if (!logContent) return;

        const timestamp = new Date().toLocaleTimeString();
        const typeClass = {
            'info': 'text-info',
            'success': 'text-success',
            'error': 'text-danger',
            'warning': 'text-warning'
        }[type] || 'text-info';

        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.innerHTML = `
            <span class="${typeClass}">[${timestamp}]</span>
            ${message}
        `;

        logContent.appendChild(logEntry);
        logContent.scrollTop = logContent.scrollHeight;
    }

    addCompletionSummary(operationId, data) {
        if (!data.statistics) return;

        const stats = data.statistics;
        const summary = [
            `📊 Processing Summary:`,
            `   📄 ${stats.total_characters?.toLocaleString() || 0} characters processed`,
            `   🧩 ${stats.total_chunks || 0} chunks created`,
            `   🏷️ ${stats.total_entities || 0} entities extracted`,
            `   📚 ${stats.total_references || 0} references found`,
            `   🔗 ${stats.total_embeddings || 0} embeddings generated`
        ];

        if (stats.entities_by_chunk && stats.entities_by_chunk.length > 0) {
            summary.push(`   📊 Entities per chunk: [${stats.entities_by_chunk.join(', ')}]`);
        }

        summary.forEach(line => {
            this.addLogEntry(operationId, line, 'success');
        });
    }

    // Utility methods
    getStepBackgroundColor(status) {
        const colors = {
            'pending': 'rgba(255,255,255,0.1)',
            'in_progress': 'rgba(255,193,7,0.3)',
            'completed': 'rgba(40,167,69,0.3)',
            'failed': 'rgba(220,53,69,0.3)'
        };
        return colors[status] || colors.pending;
    }

    getStepIcon(stepKey, status) {
        const baseIcons = {
            'initializing': 'bi-gear',
            'text_extraction': 'bi-file-text',
            'reference_extraction': 'bi-bookmark',
            'entity_extraction': 'bi-tags',
            'chunk_processing': 'bi-puzzle',
            'embedding_generation': 'bi-vector-pen',
            'metadata_extraction': 'bi-info-circle',
            'finalization': 'bi-check-circle'
        };

        const baseIcon = baseIcons[stepKey] || 'bi-circle';

        if (status === 'in_progress') {
            return `${baseIcon} text-warning`;
        } else if (status === 'completed') {
            return `bi-check-circle-fill text-success`;
        } else if (status === 'failed') {
            return `bi-x-circle-fill text-danger`;
        }

        return `${baseIcon} text-muted`;
    }

    getDefaultStepMessage(status) {
        const messages = {
            'pending': 'Waiting...',
            'in_progress': 'Processing...',
            'completed': 'Completed',
            'failed': 'Failed'
        };
        return messages[status] || 'Unknown';
    }

    formatStepStatus(status) {
        const formatted = {
            'pending': 'Pending',
            'in_progress': 'In Progress',
            'completed': 'Completed',
            'failed': 'Failed'
        };
        return formatted[status] || status;
    }

    getStepBadgeClass(status) {
        const classes = {
            'pending': 'bg-secondary',
            'in_progress': 'bg-warning text-dark',
            'completed': 'bg-success',
            'failed': 'bg-danger'
        };
        return classes[status] || 'bg-secondary';
    }

    formatDuration(seconds) {
        if (seconds < 60) {
            return `${Math.round(seconds)}s`;
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.round(seconds % 60);
            return `${minutes}m ${remainingSeconds}s`;
        } else {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}h ${minutes}m`;
        }
    }

    toggleLog(operationId) {
        const logContainer = document.getElementById(`log-container-${operationId}`);
        const toggleIcon = document.getElementById(`log-toggle-${operationId}`);

        if (logContainer && toggleIcon) {
            if (logContainer.style.display === 'none') {
                logContainer.style.display = 'block';
                toggleIcon.className = 'bi bi-chevron-up';
            } else {
                logContainer.style.display = 'none';
                toggleIcon.className = 'bi bi-chevron-down';
            }
        }
    }

    cancelOperation(operationId) {
        const websocket = this.websockets.get(operationId);
        if (websocket) {
            websocket.close();
        }

        this.addLogEntry(operationId, 'Operation cancelled by user', 'warning');

        // Update UI to show cancelled state
        const card = document.getElementById(`enhanced-progress-${operationId}`);
        if (card) {
            card.style.background = 'linear-gradient(135deg, #6c757d 0%, #495057 100%)';
        }

        const currentMessage = document.getElementById(`current-message-${operationId}`);
        if (currentMessage) {
            currentMessage.textContent = 'Operation cancelled';
        }

        setTimeout(() => {
            this.hideOperation(operationId);
        }, 3000);
    }

    hideOperation(operationId) {
        const card = document.getElementById(`enhanced-progress-${operationId}`);
        if (card) {
            card.style.transition = 'opacity 0.5s ease-out';
            card.style.opacity = '0';
            setTimeout(() => {
                card.remove();
            }, 500);
        }

        // Clean up
        this.activeOperations.delete(operationId);
        const websocket = this.websockets.get(operationId);
        if (websocket) {
            websocket.close();
            this.websockets.delete(operationId);
        }
    }

    stopOperation(operationId) {
        console.log(`🛑 Stopping operation: ${operationId}`);

        // Send stop signal via WebSocket
        const websocket = this.websockets.get(operationId);
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            websocket.send(JSON.stringify({
                type: 'cancel_operation',
                operation_id: operationId
            }));
        }

        // Update UI to show stopped state
        const statusElement = document.getElementById(`step-indicator-${operationId}`);
        if (statusElement) {
            statusElement.textContent = 'Stopped';
            statusElement.className = 'badge bg-danger text-white me-3 fs-6';
        }

        // Disable stop button, enable clear button
        const stopBtn = document.getElementById(`stop-btn-${operationId}`);
        const clearBtn = document.getElementById(`clear-btn-${operationId}`);
        if (stopBtn) stopBtn.disabled = true;
        if (clearBtn) clearBtn.disabled = false;

        this.addLogEntry(operationId, 'Processing stopped by user', 'warning');
    }

    clearOperation(operationId) {
        console.log(`🧹 Clearing operation: ${operationId}`);

        // Remove from active operations
        this.activeOperations.delete(operationId);

        // Hide the progress card
        this.hideOperation(operationId);

        // Close WebSocket connection
        const websocket = this.websockets.get(operationId);
        if (websocket) {
            websocket.close();
            this.websockets.delete(operationId);
        }
    }
}

// Create global instance
const enhancedProgressUI = new EnhancedProgressUI();

// Make it globally accessible
window.enhancedProgressUI = enhancedProgressUI;

// Debug logging
console.log('🚀 Enhanced Progress UI loaded and initialized');
console.log('📊 Enhanced Progress UI instance:', enhancedProgressUI);
console.log('📊 Global window.enhancedProgressUI:', window.enhancedProgressUI);
