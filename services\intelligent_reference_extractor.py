#!/usr/bin/env python3
"""
Intelligent Reference Extractor - Advanced AI-powered reference extraction
Uses sophisticated pattern matching and AI analysis to extract ALL references from documents.
"""

import re
import asyncio
import json
from typing import List, Dict, Any, Set
from pathlib import Path
from utils.logging_utils import get_logger
from utils.open_router_client import get_open_router_client

logger = get_logger(__name__)

class IntelligentReferenceExtractor:
    """
    Advanced reference extractor using multiple strategies and AI analysis.
    Designed to catch ALL references that traditional methods miss.
    """
    
    def __init__(self):
        try:
            self.client = get_open_router_client()
            self.ai_available = True
        except Exception as e:
            logger.warning(f"AI client not available: {e}")
            self.client = None
            self.ai_available = False
        
        # Comprehensive patterns for different reference formats
        self.patterns = {
            'author_year_journal': re.compile(
                r'([A-Z][a-z]+(?:,?\s+[A-Z]\.?)*(?:\s+et\s+al\.?)?)\s*[\(\[]?(\d{4})[^\.\n]*?\.?\s*([A-Z][^\.]+)\.?\s*(\d+(?:\(\d+\))?)[^\d]*(\d+[-–]\d+)',
                re.MULTILINE
            ),
            'author_year_simple': re.compile(
                r'([A-Z][a-z]+(?:,?\s+[A-Z]\.?)*(?:\s+et\s+al\.?)?)\s*[\(\[]?(\d{4})[^\.\n]*?\.([^\.]+\.)(?:\s*(\d+(?:\(\d+\))?)[^\d]*(\d+[-–]\d+))?',
                re.MULTILINE
            ),
            'doi_pattern': re.compile(
                r'doi:\s*([^\s\n]+)|https?://doi\.org/([^\s\n]+)',
                re.IGNORECASE
            ),
            'pmid_pattern': re.compile(
                r'PMID:\s*(\d+)',
                re.IGNORECASE
            ),
            'journal_volume_pages': re.compile(
                r'([A-Z][^\.]+)\.\s*(\d{4})[^\d]*(\d+(?:\(\d+\))?)[^\d]*(\d+[-–]\d+)',
                re.MULTILINE
            ),
            'nature_format': re.compile(
                r'([A-Z][a-z]+(?:,?\s+[A-Z]\.?)*)\s*\((\d{4})\)\.\s*([^,]+),\s*(\d+(?:\(\d+\))?),\s*(\d+[-–]\d+)',
                re.MULTILINE
            ),
            'bmj_format': re.compile(
                r'([A-Z][a-z]+(?:,?\s+[A-Z]\.?)*)\s*([A-Z][^\.]+)\.\s*(\d{4})[^\d]*(\d+(?:\(\d+\))?)[^\d]*(\d+[-–]\d+)',
                re.MULTILINE
            ),
            'government_report': re.compile(
                r'([A-Z][^\.]+)\.\s*([^\.]+)\.\s*([A-Z][^\.]+)\.\s*(\d{4})',
                re.MULTILINE
            )
        }
    
    async def extract_references_comprehensive(self, text: str, document_name: str) -> Dict[str, Any]:
        """
        Comprehensive reference extraction using multiple strategies.
        """
        logger.info(f"🧠 Starting INTELLIGENT reference extraction for {document_name}")
        logger.info(f"📄 Document length: {len(text):,} characters")
        
        all_references = set()
        extraction_methods = {}
        
        # Strategy 1: Pattern-based extraction
        pattern_refs = await self._extract_with_patterns(text)
        all_references.update(pattern_refs)
        extraction_methods['pattern_based'] = len(pattern_refs)
        logger.info(f"🔍 Pattern-based extraction: {len(pattern_refs)} references")
        
        # Strategy 2: Section-based extraction
        section_refs = await self._extract_from_sections(text)
        all_references.update(section_refs)
        extraction_methods['section_based'] = len(section_refs)
        logger.info(f"📚 Section-based extraction: {len(section_refs)} references")
        
        # Strategy 3: AI-powered extraction using Mistral (if available)
        if self.ai_available:
            ai_refs = await self._extract_with_ai(text, document_name)
            all_references.update(ai_refs)
            extraction_methods['ai_powered'] = len(ai_refs)
            logger.info(f"🤖 AI-powered extraction: {len(ai_refs)} references")
        else:
            extraction_methods['ai_powered'] = 0
            logger.info(f"🤖 AI-powered extraction: Skipped (not available)")
        
        # Strategy 4: Line-by-line analysis
        line_refs = await self._extract_line_by_line(text)
        all_references.update(line_refs)
        extraction_methods['line_analysis'] = len(line_refs)
        logger.info(f"📝 Line-by-line analysis: {len(line_refs)} references")
        
        # Convert to list and clean
        final_references = list(all_references)
        cleaned_references = await self._clean_and_validate_references(final_references)
        
        logger.info(f"✅ INTELLIGENT extraction complete: {len(cleaned_references)} unique references found")
        
        return {
            'references': [{'text': ref, 'confidence': 0.9} for ref in cleaned_references],
            'total_found': len(cleaned_references),
            'extraction_methods': extraction_methods,
            'confidence_score': 0.95,
            'document_name': document_name
        }
    
    async def _extract_with_patterns(self, text: str) -> Set[str]:
        """Extract references using comprehensive pattern matching."""
        references = set()
        
        for pattern_name, pattern in self.patterns.items():
            matches = pattern.findall(text)
            for match in matches:
                if isinstance(match, tuple):
                    # Reconstruct reference from tuple
                    ref_text = ' '.join(str(part) for part in match if part)
                    if len(ref_text) > 20:  # Minimum length filter
                        references.add(ref_text.strip())
                else:
                    if len(match) > 20:
                        references.add(match.strip())
        
        return references
    
    async def _extract_from_sections(self, text: str) -> Set[str]:
        """Extract references from dedicated reference sections."""
        references = set()
        
        # Look for reference sections
        ref_section_patterns = [
            r'REFERENCES?\s*\n(.*?)(?=\n[A-Z]{2,}|\Z)',
            r'Bibliography\s*\n(.*?)(?=\n[A-Z]{2,}|\Z)',
            r'Literature Cited\s*\n(.*?)(?=\n[A-Z]{2,}|\Z)',
            r'Works Cited\s*\n(.*?)(?=\n[A-Z]{2,}|\Z)'
        ]
        
        for pattern in ref_section_patterns:
            matches = re.findall(pattern, text, re.DOTALL | re.IGNORECASE)
            for match in matches:
                # Split into individual references
                lines = match.split('\n')
                for line in lines:
                    line = line.strip()
                    if len(line) > 30 and self._looks_like_reference(line):
                        references.add(line)
        
        return references
    
    async def _extract_with_ai(self, text: str, document_name: str) -> Set[str]:
        """Use AI to extract references that patterns might miss."""
        try:
            # Split text into chunks for AI processing
            chunk_size = 8000  # Manageable chunk size for AI
            chunks = [text[i:i+chunk_size] for i in range(0, len(text), chunk_size)]
            
            all_ai_refs = set()
            
            for i, chunk in enumerate(chunks[:5]):  # Process first 5 chunks to avoid rate limits
                logger.info(f"🤖 Processing chunk {i+1}/{min(5, len(chunks))} with AI")
                
                prompt = self._create_ai_extraction_prompt(chunk, document_name)
                
                try:
                    response = await self.client.generate_completion_async(
                        messages=[{"role": "user", "content": prompt}],
                        temperature=0.1,
                        max_tokens=2000
                    )
                    
                    if response and 'content' in response:
                        content = response['content']
                        ai_refs = self._parse_ai_response(content)
                        all_ai_refs.update(ai_refs)
                        logger.info(f"🤖 AI found {len(ai_refs)} references in chunk {i+1}")
                    elif response and 'choices' in response:
                        content = response['choices'][0]['message']['content']
                        ai_refs = self._parse_ai_response(content)
                        all_ai_refs.update(ai_refs)
                        logger.info(f"🤖 AI found {len(ai_refs)} references in chunk {i+1}")
                    
                    # Small delay to avoid rate limiting
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.warning(f"AI extraction failed for chunk {i+1}: {e}")
                    continue
            
            return all_ai_refs
            
        except Exception as e:
            logger.error(f"AI-powered extraction failed: {e}")
            return set()
    
    def _create_ai_extraction_prompt(self, text: str, document_name: str) -> str:
        """Create a comprehensive prompt for AI reference extraction."""
        return f"""You are an expert academic reference extractor. Your task is to find ALL bibliographic references in the following text from "{document_name}".

EXTRACT EVERY SINGLE REFERENCE including:
- Journal articles (Author, Year, Title, Journal, Volume, Pages)
- Books and book chapters
- Government reports and institutional publications
- Conference proceedings
- Theses and dissertations
- Web resources with URLs
- DOI references
- Any citation that follows academic formatting

IMPORTANT RULES:
1. Extract COMPLETE references, not partial citations
2. Include author names, publication years, titles, and publication details
3. Preserve original formatting and punctuation
4. Do NOT miss any references - be exhaustive
5. Include references that may be embedded in text, not just in reference lists
6. Look for patterns like "Author (Year)" or "Author et al. (Year)"

FORMAT: Return each reference on a separate line, starting with "REF:" followed by the complete reference.

TEXT TO ANALYZE:
{text[:7000]}

Extract ALL references now:"""
    
    def _parse_ai_response(self, content: str) -> Set[str]:
        """Parse AI response to extract references."""
        references = set()
        
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('REF:'):
                ref = line[4:].strip()
                if len(ref) > 20:
                    references.add(ref)
            elif self._looks_like_reference(line) and len(line) > 30:
                references.add(line)
        
        return references
    
    async def _extract_line_by_line(self, text: str) -> Set[str]:
        """Analyze each line to find potential references."""
        references = set()
        
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            
            # Skip very short lines
            if len(line) < 30:
                continue
            
            # Check if line looks like a reference
            if self._looks_like_reference(line):
                references.add(line)
        
        return references
    
    def _looks_like_reference(self, text: str) -> bool:
        """Determine if a text string looks like a bibliographic reference."""
        # Check for common reference indicators
        indicators = [
            r'\b\d{4}\b',  # Year
            r'\b\d+\(\d+\)',  # Volume(Issue)
            r'\b\d+[-–]\d+',  # Page range
            r'doi:',  # DOI
            r'https?://',  # URL
            r'et\s+al\.?',  # Et al
            r'[A-Z][a-z]+,\s*[A-Z]\.?',  # Author format
            r'Vol\.\s*\d+',  # Volume
            r'pp?\.\s*\d+',  # Pages
        ]
        
        matches = sum(1 for pattern in indicators if re.search(pattern, text, re.IGNORECASE))
        
        # Must have at least 2 indicators to be considered a reference
        return matches >= 2
    
    async def _clean_and_validate_references(self, references: List[str]) -> List[str]:
        """Clean and validate extracted references."""
        cleaned = []
        
        for ref in references:
            # Basic cleaning
            ref = ref.strip()
            ref = re.sub(r'\s+', ' ', ref)  # Normalize whitespace
            ref = re.sub(r'^[\d\.\-\s]+', '', ref)  # Remove leading numbers
            
            # Skip if too short or doesn't look like a reference
            if len(ref) < 30 or not self._looks_like_reference(ref):
                continue
            
            # Skip duplicates (case-insensitive)
            if not any(ref.lower() in existing.lower() or existing.lower() in ref.lower() 
                      for existing in cleaned):
                cleaned.append(ref)
        
        return cleaned

# Factory function
def get_intelligent_reference_extractor():
    """Get an instance of the intelligent reference extractor."""
    return IntelligentReferenceExtractor()
