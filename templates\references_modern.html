{% extends "base_modern.html" %}

{% block title %}References - Graphiti{% endblock %}

{% block content %}
<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-robot text-primary"></i> AI-Powered Reference Library
        </h1>
        <p class="text-muted">
            Intelligent reference extraction with AI enhancement
            <span class="badge bg-success ms-2">
                <i class="bi bi-cpu"></i> AI-Enhanced
            </span>
        </p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="extractReferences()">
            <i class="bi bi-magic"></i> AI Extract References
        </button>
        <button class="btn btn-outline-info ms-2" onclick="reprocessAllDocuments()">
            <i class="bi bi-arrow-repeat"></i> Reprocess All
        </button>
        <button class="btn btn-outline-success ms-2" onclick="exportReferences()">
            <i class="bi bi-download"></i> Export CSV
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="refreshReferences()">
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card gradient-card stats-card">
            <div class="card-body">
                <div class="stats-number" id="total-references">-</div>
                <div class="stats-label">
                    <i class="bi bi-bookmark"></i> Total References
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card gradient-card-success stats-card">
            <div class="card-body">
                <div class="stats-number" id="ai-enhanced-refs">-</div>
                <div class="stats-label">
                    <i class="bi bi-robot"></i> AI-Enhanced
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card gradient-card-warning stats-card">
            <div class="card-body">
                <div class="stats-number" id="high-quality-refs">-</div>
                <div class="stats-label">
                    <i class="bi bi-star"></i> High Quality
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card gradient-card-info stats-card">
            <div class="card-body">
                <div class="stats-number" id="unique-documents">-</div>
                <div class="stats-label">
                    <i class="bi bi-file-text"></i> Documents
                </div>
            </div>
        </div>
    </div>
</div>

<!-- AI Enhancement Status & Quality Metrics -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-cpu"></i> AI Enhancement Status</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <i class="bi bi-robot fs-3 text-primary"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">AI Model</h6>
                                <small class="text-muted">meta-llama/llama-4-maverick</small>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <i class="bi bi-lightning fs-3 text-warning"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">Token Limit</h6>
                                <small class="text-muted">4,000 tokens per chunk</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <i class="bi bi-shield-check fs-3 text-success"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">Quality Filtering</h6>
                                <small class="text-muted">AI-powered quality scoring</small>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <i class="bi bi-arrow-repeat fs-3 text-info"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">Deduplication</h6>
                                <small class="text-muted">Intelligent duplicate removal</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-bar-chart"></i> Quality Distribution</h5>
            </div>
            <div class="card-body">
                <div id="quality-distribution">
                    <div class="text-center py-4">
                        <div class="loading-spinner"></div>
                        <p class="text-muted mt-2">Loading quality metrics...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5><i class="bi bi-funnel"></i> Search & Filters</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" class="form-control" id="search-input" placeholder="Search references...">
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="year-filter">
                    <option value="">All Years</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="journal-filter">
                    <option value="">All Journals</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="sort-by">
                    <option value="quality_score">Quality Score</option>
                    <option value="confidence">AI Confidence</option>
                    <option value="date">Date</option>
                    <option value="title">Title</option>
                    <option value="authors">Authors</option>
                    <option value="journal">Journal</option>
                </select>
            </div>
            <div class="col-md-1">
                <select class="form-select" id="sort-order">
                    <option value="desc">Desc</option>
                    <option value="asc">Asc</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary w-100" onclick="applyFilters()">
                    <i class="bi bi-funnel"></i> Apply
                </button>
            </div>
        </div>
    </div>
</div>

<!-- References List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="bi bi-list-ul"></i> Reference Collection</h5>
        <div>
            <button class="btn btn-sm btn-outline-primary" onclick="toggleView('list')">
                <i class="bi bi-list"></i> List
            </button>
            <button class="btn btn-sm btn-outline-primary ms-1" onclick="toggleView('cards')">
                <i class="bi bi-grid-3x3"></i> Cards
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <!-- Loading State -->
        <div id="references-loading" class="text-center py-5">
            <div class="loading-spinner"></div>
            <p class="text-muted mt-2">Loading references...</p>
        </div>
        
        <!-- List View -->
        <div id="list-view" style="display: none;">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>Reference</th>
                            <th>Quality Score</th>
                            <th>AI Confidence</th>
                            <th>Source Document</th>
                            <th>Extraction Method</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="references-table-body">
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Cards View -->
        <div id="cards-view" style="display: none;">
            <div class="row p-3" id="references-cards">
            </div>
        </div>
        
        <!-- Empty State -->
        <div id="empty-state" class="text-center py-5" style="display: none;">
            <i class="bi bi-bookmark-x fs-1 text-muted mb-3"></i>
            <h5>No References Found</h5>
            <p class="text-muted">Process documents to extract references</p>
            <button class="btn btn-primary" onclick="window.location.href='/enhanced-upload'">
                <i class="bi bi-plus-circle"></i> Upload Documents
            </button>
        </div>
    </div>
    
    <!-- Pagination -->
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <small class="text-muted">
                    Showing <span id="showing-start">0</span> to <span id="showing-end">0</span> 
                    of <span id="total-count">0</span> references
                </small>
            </div>
            <nav>
                <ul class="pagination pagination-sm mb-0" id="pagination">
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Reference Details Modal -->
<div class="modal fade" id="reference-details-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reference Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="reference-details-content">
                <div class="text-center py-4">
                    <div class="loading-spinner"></div>
                    <p class="text-muted mt-2">Loading reference details...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="copy-citation-btn">
                    <i class="bi bi-clipboard"></i> Copy Citation
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentLimit = 20;
let currentView = 'list';
let references = [];
let selectedReferenceId = null;

document.addEventListener('DOMContentLoaded', function() {
    loadReferences();
    loadFilterOptions();
    setupEventListeners();
});

function setupEventListeners() {
    // Search input with debounce
    let searchTimeout;
    document.getElementById('search-input').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            currentPage = 1;
            loadReferences();
        }, 500);
    });
    
    // Filter change handlers
    ['year-filter', 'journal-filter', 'sort-by', 'sort-order'].forEach(id => {
        document.getElementById(id).addEventListener('change', () => {
            currentPage = 1;
            loadReferences();
        });
    });
}

async function loadReferences() {
    try {
        showLoading();
        
        const searchTerm = document.getElementById('search-input').value;
        const year = document.getElementById('year-filter').value;
        const journal = document.getElementById('journal-filter').value;
        const sortBy = document.getElementById('sort-by').value;
        const sortOrder = document.getElementById('sort-order').value;
        
        const params = new URLSearchParams({
            page: currentPage,
            limit: currentLimit,
            sort_by: sortBy,
            sort_order: sortOrder
        });
        
        if (searchTerm) params.append('search', searchTerm);
        if (year) params.append('year', year);
        if (journal) params.append('journal', journal);
        
        const response = await fetch(`/api/references?${params}`);
        if (!response.ok) throw new Error('Failed to load references');
        
        const data = await response.json();
        references = data.references || [];
        
        updateStats(data);
        displayReferences(references);
        updatePagination(data.pagination || {});
        
    } catch (error) {
        console.error('Error loading references:', error);
        showAlert('Error loading references: ' + error.message, 'danger');
        showEmptyState();
    }
}

async function loadFilterOptions() {
    try {
        // Load years
        const yearsResponse = await fetch('/api/references/years');
        if (yearsResponse.ok) {
            const yearsData = await yearsResponse.json();
            const yearSelect = document.getElementById('year-filter');
            yearSelect.innerHTML = '<option value="">All Years</option>';
            
            yearsData.years.forEach(year => {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;
                yearSelect.appendChild(option);
            });
        }
        
        // Load journals
        const journalsResponse = await fetch('/api/references/journals');
        if (journalsResponse.ok) {
            const journalsData = await journalsResponse.json();
            const journalSelect = document.getElementById('journal-filter');
            journalSelect.innerHTML = '<option value="">All Journals</option>';
            
            journalsData.journals.slice(0, 20).forEach(journal => {
                const option = document.createElement('option');
                option.value = journal.name;
                option.textContent = `${journal.name} (${journal.count})`;
                journalSelect.appendChild(option);
            });
        }
        
    } catch (error) {
        console.error('Error loading filter options:', error);
    }
}

function updateStats(data) {
    document.getElementById('total-references').textContent = data.total_count || 0;

    // Calculate AI-enhanced references
    const aiEnhanced = references.filter(r => r.extraction_method === 'ai_powered_intelligent' || r.ai_enhanced).length;
    document.getElementById('ai-enhanced-refs').textContent = aiEnhanced;

    // Calculate high quality references (quality score > 0.7)
    const highQuality = references.filter(r => (r.quality_score || 0) > 0.7).length;
    document.getElementById('high-quality-refs').textContent = highQuality;

    // Calculate unique documents
    const uniqueDocs = new Set(references.map(r => r.source_document).filter(d => d)).size;
    document.getElementById('unique-documents').textContent = uniqueDocs;

    loadQualityDistribution();
}

function displayReferences(refs) {
    hideLoading();
    
    if (!refs || refs.length === 0) {
        showEmptyState();
        return;
    }
    
    if (currentView === 'list') {
        displayListView(refs);
    } else {
        displayCardsView(refs);
    }
}

function displayListView(refs) {
    const tbody = document.getElementById('references-table-body');
    tbody.innerHTML = '';

    refs.forEach(ref => {
        const row = document.createElement('tr');
        row.className = 'fade-in';

        // Quality score badge color
        const qualityScore = ref.quality_score || 0;
        let qualityBadgeClass = 'bg-secondary';
        if (qualityScore > 0.7) qualityBadgeClass = 'bg-success';
        else if (qualityScore > 0.4) qualityBadgeClass = 'bg-warning';
        else if (qualityScore > 0) qualityBadgeClass = 'bg-danger';

        // Extraction method badge
        const isAiEnhanced = ref.extraction_method === 'ai_powered_intelligent' || ref.ai_enhanced;
        const methodBadge = isAiEnhanced ?
            '<span class="badge bg-primary"><i class="bi bi-robot"></i> AI</span>' :
            '<span class="badge bg-secondary">Pattern</span>';

        row.innerHTML = `
            <td>
                <div>
                    <h6 class="mb-1">${ref.reference_text ? ref.reference_text.substring(0, 80) + '...' : 'No text'}</h6>
                    <small class="text-muted">${ref.source_document || 'Unknown document'}</small>
                </div>
            </td>
            <td>
                <span class="badge ${qualityBadgeClass}">${qualityScore.toFixed(2)}</span>
            </td>
            <td>
                <span class="badge bg-info">${(ref.confidence || 0).toFixed(2)}</span>
            </td>
            <td>
                <small>${ref.source_document || 'Unknown'}</small>
            </td>
            <td>
                ${methodBadge}
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewReference('${ref.reference_id || ref.uuid}')">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="copyReference('${ref.reference_id || ref.uuid}')">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });

    document.getElementById('list-view').style.display = 'block';
    document.getElementById('cards-view').style.display = 'none';
}

function displayCardsView(refs) {
    const cards = document.getElementById('references-cards');
    cards.innerHTML = '';
    
    refs.forEach(ref => {
        const card = document.createElement('div');
        card.className = 'col-md-6 col-lg-4 mb-3';
        card.innerHTML = `
            <div class="card h-100 fade-in">
                <div class="card-body">
                    <h6 class="card-title">${ref.title || 'Untitled'}</h6>
                    <p class="card-text">
                        <small class="text-muted">
                            ${ref.authors ? ref.authors.substring(0, 80) + (ref.authors.length > 80 ? '...' : '') : 'Unknown authors'}
                        </small>
                    </p>
                    <div class="mb-2">
                        <span class="badge bg-primary">${ref.year || 'N/A'}</span>
                        ${ref.journal ? `<span class="badge bg-secondary ms-1">${ref.journal.substring(0, 20)}${ref.journal.length > 20 ? '...' : ''}</span>` : ''}
                    </div>
                    ${ref.abstract ? `
                        <p class="card-text">
                            <small>${ref.abstract.substring(0, 120)}${ref.abstract.length > 120 ? '...' : ''}</small>
                        </p>
                    ` : ''}
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100">
                        <button class="btn btn-outline-primary btn-sm" onclick="viewReference('${ref.uuid}')">
                            <i class="bi bi-eye"></i> View
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="copyReference('${ref.uuid}')">
                            <i class="bi bi-clipboard"></i> Copy
                        </button>
                        ${ref.doi ? `
                            <a href="https://doi.org/${ref.doi}" target="_blank" class="btn btn-outline-success btn-sm">
                                <i class="bi bi-link-45deg"></i> DOI
                            </a>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
        cards.appendChild(card);
    });
    
    document.getElementById('cards-view').style.display = 'block';
    document.getElementById('list-view').style.display = 'none';
}

async function loadQualityDistribution() {
    try {
        const container = document.getElementById('quality-distribution');

        if (references.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">No references available</p>';
            return;
        }

        // Calculate quality distribution
        const qualityRanges = {
            'High (0.7-1.0)': 0,
            'Medium (0.4-0.7)': 0,
            'Low (0.1-0.4)': 0,
            'Unscored (0.0)': 0
        };

        references.forEach(ref => {
            const score = ref.quality_score || 0;
            if (score >= 0.7) qualityRanges['High (0.7-1.0)']++;
            else if (score >= 0.4) qualityRanges['Medium (0.4-0.7)']++;
            else if (score > 0) qualityRanges['Low (0.1-0.4)']++;
            else qualityRanges['Unscored (0.0)']++;
        });

        const total = references.length;

        container.innerHTML = Object.entries(qualityRanges).map(([range, count]) => {
            const percentage = total > 0 ? (count / total * 100).toFixed(1) : 0;
            const badgeClass = range.includes('High') ? 'bg-success' :
                              range.includes('Medium') ? 'bg-warning' :
                              range.includes('Low') ? 'bg-danger' : 'bg-secondary';

            return `
                <div class="d-flex justify-content-between align-items-center mb-2 slide-in">
                    <div>
                        <small class="fw-bold">${range}</small>
                        <br>
                        <small class="text-muted">${percentage}%</small>
                    </div>
                    <span class="badge ${badgeClass}">${count}</span>
                </div>
            `;
        }).join('');

    } catch (error) {
        console.error('Error loading quality distribution:', error);
    }
}

function createTimelineChart() {
    const ctx = document.getElementById('timeline-chart').getContext('2d');
    
    // Group references by year
    const yearCounts = {};
    references.forEach(ref => {
        if (ref.year) {
            yearCounts[ref.year] = (yearCounts[ref.year] || 0) + 1;
        }
    });
    
    const years = Object.keys(yearCounts).sort();
    const counts = years.map(year => yearCounts[year]);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: years,
            datasets: [{
                label: 'Publications',
                data: counts,
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
}

async function viewReference(referenceId) {
    try {
        selectedReferenceId = referenceId;
        const modal = new bootstrap.Modal(document.getElementById('reference-details-modal'));
        modal.show();
        
        const response = await fetch(`/api/references/${referenceId}`);
        if (!response.ok) throw new Error('Failed to load reference details');
        
        const ref = await response.json();
        displayReferenceDetails(ref);
        
    } catch (error) {
        console.error('Error viewing reference:', error);
        showAlert('Error loading reference details: ' + error.message, 'danger');
    }
}

function displayReferenceDetails(ref) {
    const content = document.getElementById('reference-details-content');

    // Quality score badge
    const qualityScore = ref.quality_score || 0;
    let qualityBadgeClass = 'bg-secondary';
    if (qualityScore > 0.7) qualityBadgeClass = 'bg-success';
    else if (qualityScore > 0.4) qualityBadgeClass = 'bg-warning';
    else if (qualityScore > 0) qualityBadgeClass = 'bg-danger';

    // AI enhancement status
    const isAiEnhanced = ref.extraction_method === 'ai_powered_intelligent' || ref.ai_enhanced;

    content.innerHTML = `
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <h5>${ref.reference_text ? ref.reference_text.substring(0, 100) + '...' : 'Reference Details'}</h5>
                    <div>
                        ${isAiEnhanced ? '<span class="badge bg-primary me-1"><i class="bi bi-robot"></i> AI Enhanced</span>' : ''}
                        <span class="badge ${qualityBadgeClass}">Quality: ${qualityScore.toFixed(2)}</span>
                    </div>
                </div>

                <div class="alert alert-info">
                    <strong>Full Reference Text:</strong><br>
                    ${ref.reference_text || 'No reference text available'}
                </div>

                <table class="table table-sm">
                    <tr><td><strong>Source Document:</strong></td><td>${ref.source_document || 'Unknown'}</td></tr>
                    <tr><td><strong>Extraction Method:</strong></td><td>
                        ${isAiEnhanced ?
                            '<span class="badge bg-primary"><i class="bi bi-robot"></i> AI-Powered Intelligent</span>' :
                            '<span class="badge bg-secondary">Pattern-Based</span>'
                        }
                    </td></tr>
                    <tr><td><strong>Quality Score:</strong></td><td>
                        <span class="badge ${qualityBadgeClass}">${qualityScore.toFixed(3)}</span>
                        <small class="text-muted ms-2">(0.0 - 1.0 scale)</small>
                    </td></tr>
                    <tr><td><strong>AI Confidence:</strong></td><td>
                        <span class="badge bg-info">${(ref.confidence || 0).toFixed(3)}</span>
                    </td></tr>
                    <tr><td><strong>Reference Length:</strong></td><td>${ref.reference_length || (ref.reference_text ? ref.reference_text.length : 0)} characters</td></tr>
                    <tr><td><strong>Has DOI:</strong></td><td>
                        ${ref.has_doi ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}
                    </td></tr>
                    <tr><td><strong>Has Year:</strong></td><td>
                        ${ref.has_year ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}
                    </td></tr>
                    <tr><td><strong>Has Journal:</strong></td><td>
                        ${ref.has_journal ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}
                    </td></tr>
                    <tr><td><strong>Processed At:</strong></td><td>${ref.processed_at ? new Date(ref.processed_at).toLocaleString() : 'Unknown'}</td></tr>
                </table>

                ${ref.normalized_text ? `
                    <div class="mt-3">
                        <h6>Normalized Text (for deduplication)</h6>
                        <p class="text-muted small">${ref.normalized_text}</p>
                    </div>
                ` : ''}
            </div>
        </div>
    `;

    // Set up copy citation button
    document.getElementById('copy-citation-btn').onclick = () => copyReference(ref.reference_id || ref.uuid);
}

async function copyReference(referenceId) {
    try {
        const ref = references.find(r => r.uuid === referenceId);
        if (!ref) throw new Error('Reference not found');
        
        // Generate APA citation
        let citation = '';
        if (ref.authors) citation += ref.authors + '. ';
        if (ref.year) citation += `(${ref.year}). `;
        if (ref.title) citation += ref.title + '. ';
        if (ref.journal) citation += `${ref.journal}`;
        if (ref.volume) citation += `, ${ref.volume}`;
        if (ref.issue) citation += `(${ref.issue})`;
        if (ref.pages) citation += `, ${ref.pages}`;
        if (ref.doi) citation += `. https://doi.org/${ref.doi}`;
        
        await navigator.clipboard.writeText(citation);
        showAlert('Citation copied to clipboard', 'success', 2000);
        
    } catch (error) {
        console.error('Error copying reference:', error);
        showAlert('Error copying citation: ' + error.message, 'danger');
    }
}

async function extractReferences() {
    try {
        showAlert('Starting AI-powered reference extraction...', 'info');

        const response = await fetch('/api/references/extract-ai', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ai_enhanced: true,
                quality_filtering: true
            })
        });

        if (response.ok) {
            const result = await response.json();
            showAlert('AI-powered reference extraction started in background', 'success');
            setTimeout(() => {
                loadReferences();
            }, 3000);
        } else {
            throw new Error('Failed to start AI reference extraction');
        }
    } catch (error) {
        console.error('Error extracting references:', error);
        showAlert('Error starting AI reference extraction: ' + error.message, 'danger');
    }
}

async function reprocessAllDocuments() {
    try {
        const confirmed = confirm(
            'This will reprocess ALL documents with AI-powered reference extraction. ' +
            'This may take a significant amount of time. Continue?'
        );

        if (!confirmed) return;

        showAlert('Starting comprehensive document reprocessing with AI...', 'info');

        const response = await fetch('/api/references/reprocess-all', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ai_enhanced: true,
                quality_filtering: true,
                update_master_csv: true
            })
        });

        if (response.ok) {
            const result = await response.json();
            showAlert(
                `Document reprocessing started! Processing ${result.documents_found || 'unknown'} documents with AI enhancement.`,
                'success'
            );

            // Refresh references every 30 seconds during reprocessing
            const refreshInterval = setInterval(() => {
                loadReferences();
            }, 30000);

            // Stop refreshing after 2 hours
            setTimeout(() => {
                clearInterval(refreshInterval);
            }, 7200000);

        } else {
            throw new Error('Failed to start document reprocessing');
        }
    } catch (error) {
        console.error('Error reprocessing documents:', error);
        showAlert('Error starting document reprocessing: ' + error.message, 'danger');
    }
}

async function exportReferences() {
    try {
        const response = await fetch('/api/references/export');
        if (!response.ok) throw new Error('Failed to export references');
        
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `references-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
        
        showAlert('References exported successfully', 'success', 2000);
        
    } catch (error) {
        console.error('Error exporting references:', error);
        showAlert('Error exporting references: ' + error.message, 'danger');
    }
}

function toggleView(view) {
    currentView = view;
    displayReferences(references);
}

function applyFilters() {
    currentPage = 1;
    loadReferences();
}

function refreshReferences() {
    const button = event.target;
    const hideLoading = showLoading(button);
    
    loadReferences().finally(() => {
        hideLoading();
        showAlert('References refreshed successfully', 'success', 2000);
    });
}

function updatePagination(pagination) {
    const paginationEl = document.getElementById('pagination');
    const totalPages = pagination.total_pages || 1;
    
    // Update showing info
    document.getElementById('showing-start').textContent = ((currentPage - 1) * currentLimit) + 1;
    document.getElementById('showing-end').textContent = Math.min(currentPage * currentLimit, pagination.total_count || 0);
    document.getElementById('total-count').textContent = pagination.total_count || 0;
    
    // Generate pagination
    paginationEl.innerHTML = '';
    
    if (totalPages <= 1) return;
    
    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Previous</a>`;
    paginationEl.appendChild(prevLi);
    
    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        paginationEl.appendChild(li);
    }
    
    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Next</a>`;
    paginationEl.appendChild(nextLi);
}

function changePage(page) {
    if (page < 1) return;
    currentPage = page;
    loadReferences();
}

function showLoading() {
    document.getElementById('references-loading').style.display = 'block';
    document.getElementById('list-view').style.display = 'none';
    document.getElementById('cards-view').style.display = 'none';
    document.getElementById('empty-state').style.display = 'none';
}

function hideLoading() {
    document.getElementById('references-loading').style.display = 'none';
}

function showEmptyState() {
    hideLoading();
    document.getElementById('empty-state').style.display = 'block';
    document.getElementById('list-view').style.display = 'none';
    document.getElementById('cards-view').style.display = 'none';
}
</script>
{% endblock %}
