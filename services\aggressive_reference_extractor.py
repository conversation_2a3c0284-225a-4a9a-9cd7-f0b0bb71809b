#!/usr/bin/env python3
"""
Aggressive Reference Extractor

This module provides comprehensive reference extraction specifically designed
for medical/clinical documents, OneNote files, and presentation formats.
It uses multiple extraction strategies with lower thresholds to capture
more references from diverse document formats.
"""

import re
import asyncio
from typing import List, Dict, Any, Set, Optional
from pathlib import Path
import logging
from dataclasses import dataclass
from enum import Enum

from utils.logging_utils import get_logger

logger = get_logger(__name__)

@dataclass
class ExtractedReference:
    """Data class for extracted reference information."""
    text: str
    number: Optional[int] = None
    authors: Optional[str] = None
    title: Optional[str] = None
    journal: Optional[str] = None
    year: Optional[str] = None
    doi: Optional[str] = None
    pmid: Optional[str] = None
    url: Optional[str] = None
    confidence: float = 0.0
    extraction_method: str = "unknown"
    source_section: str = "unknown"

class ReferenceType(Enum):
    """Types of references we can extract."""
    JOURNAL_ARTICLE = "journal_article"
    BOOK = "book"
    WEBSITE = "website"
    CONFERENCE = "conference"
    THESIS = "thesis"
    PATENT = "patent"
    UNKNOWN = "unknown"

class AggressiveReferenceExtractor:
    """Aggressive reference extractor for medical/clinical documents."""
    
    def __init__(self):
        self.extracted_references: List[ExtractedReference] = []
        self.seen_texts: Set[str] = set()
        
        # Initialize comprehensive patterns
        self._init_patterns()
    
    def _init_patterns(self):
        """Initialize comprehensive reference extraction patterns."""
        
        # Reference section headers (case-insensitive)
        self.section_headers = [
            r'(?:^|\n)\s*(?:references?|bibliography|citations?|literature\s+cited|works\s+cited|sources?)\s*:?\s*(?:\n|$)',
            r'(?:^|\n)\s*(?:ref\.|refs\.|references\s+list|reference\s+list)\s*:?\s*(?:\n|$)',
            r'(?:^|\n)\s*(?:further\s+reading|additional\s+reading|suggested\s+reading)\s*:?\s*(?:\n|$)',
            r'(?:^|\n)\s*(?:selected\s+references|key\s+references)\s*:?\s*(?:\n|$)',
        ]
        
        # Numbered reference patterns (very aggressive)
        self.numbered_patterns = [
            # Standard numbered with period: 1. Author...
            r'(?:^|\n)\s*(\d{1,3})\.\s+([A-Z].*?)(?=\n\s*\d{1,3}\.|$)',
            
            # Numbered with bracket: [1] Author...
            r'(?:^|\n)\s*\[(\d{1,3})\]\s+([A-Z].*?)(?=\n\s*\[\d{1,3}\]|$)',
            
            # Dash with bracket: - [1] Author...
            r'(?:^|\n)\s*-\s*\[(\d{1,3})\]\s+([A-Z].*?)(?=\n\s*-\s*\[\d{1,3}\]|$)',
            
            # Simple number: 1 Author... (without period)
            r'(?:^|\n)\s*(\d{1,3})\s+([A-Z][a-z]+.*?)(?=\n\s*\d{1,3}\s+[A-Z]|$)',
            
            # Parenthetical: (1) Author...
            r'(?:^|\n)\s*\((\d{1,3})\)\s+([A-Z].*?)(?=\n\s*\(\d{1,3}\)|$)',
        ]
        
        # Author-year patterns
        self.author_year_patterns = [
            # Author, A. (Year). Title...
            r'([A-Z][a-z]+(?:,\s*[A-Z]\.)*(?:,\s*[A-Z][a-z]+)*)\s*\((\d{4})\)\.\s*([^.]+\..*?)(?=\n[A-Z]|$)',
            
            # Author A, Author B (Year) Title...
            r'([A-Z][a-z]+\s+[A-Z]\.?(?:,\s*[A-Z][a-z]+\s+[A-Z]\.?)*)\s*\((\d{4})\)\s*([^.]+.*?)(?=\n[A-Z]|$)',
            
            # Author et al. (Year)
            r'([A-Z][a-z]+\s+et\s+al\.?)\s*\((\d{4})\)\s*([^.]+.*?)(?=\n[A-Z]|$)',
        ]
        
        # Journal article patterns
        self.journal_patterns = [
            # Author. Title. Journal Year;Volume:Pages
            r'([A-Z][a-z]+[^.]*)\.\s*([^.]{10,200})\.\s*([A-Z][^.]{3,100})\.\s*(\d{4})[;\s]*(\d*)[:\s]*(\d*[-\d]*)',
            
            # Author. Title. Journal. Year Vol(Issue):Pages
            r'([A-Z][a-z]+[^.]*)\.\s*([^.]{10,200})\.\s*([A-Z][^.]{3,100})\.\s*(\d{4})\s*(\d*)\((\d*)\):(\d*[-\d]*)',
            
            # More flexible journal pattern
            r'([A-Z][a-z]+[^.]*)\.\s*([^.]{10,200})\.\s*([A-Z][^.]{3,50})[^.]*\.\s*(\d{4})',
        ]
        
        # DOI and identifier patterns
        self.identifier_patterns = [
            r'doi:\s*(10\.\d+/[^\s\n]+)',
            r'DOI:\s*(10\.\d+/[^\s\n]+)',
            r'PMID:\s*(\d+)',
            r'PubMed\s+ID:\s*(\d+)',
            r'ISBN:\s*([\d-]+)',
        ]
        
        # URL patterns
        self.url_patterns = [
            r'https?://[^\s\n]+',
            r'www\.[^\s\n]+',
        ]
        
        # Book patterns
        self.book_patterns = [
            # Author. Title. Publisher, Year
            r'([A-Z][a-z]+[^.]*)\.\s*([^.]{10,200})\.\s*([A-Z][^,]{3,100}),\s*(\d{4})',
            
            # Author. Title. Edition. Publisher, Year
            r'([A-Z][a-z]+[^.]*)\.\s*([^.]{10,200})\.\s*(\d+(?:st|nd|rd|th)\s+ed\.?)\.\s*([A-Z][^,]{3,100}),\s*(\d{4})',
        ]
    
    async def extract_references_from_text(self, text: str, document_name: str = "unknown") -> Dict[str, Any]:
        """
        Extract references from text using aggressive multi-strategy approach.
        
        Args:
            text: Document text to extract references from
            document_name: Name of the document for logging
            
        Returns:
            Dictionary with extracted references and metadata
        """
        logger.info(f"🚀 Starting aggressive reference extraction for {document_name}")
        logger.info(f"📄 Document length: {len(text):,} characters")
        
        self.extracted_references = []
        self.seen_texts = set()
        
        # Strategy 1: Find and process reference sections
        section_refs = await self._extract_from_reference_sections(text)
        logger.info(f"📚 Section-based extraction: {len(section_refs)} references")
        
        # Strategy 2: Extract numbered references throughout document
        numbered_refs = await self._extract_numbered_references(text)
        logger.info(f"🔢 Numbered extraction: {len(numbered_refs)} references")
        
        # Strategy 3: Extract author-year citations
        author_year_refs = await self._extract_author_year_references(text)
        logger.info(f"👥 Author-year extraction: {len(author_year_refs)} references")
        
        # Strategy 4: Extract journal articles
        journal_refs = await self._extract_journal_articles(text)
        logger.info(f"📰 Journal extraction: {len(journal_refs)} references")
        
        # Strategy 5: Extract books
        book_refs = await self._extract_books(text)
        logger.info(f"📖 Book extraction: {len(book_refs)} references")
        
        # Strategy 6: Extract URLs and DOIs
        url_refs = await self._extract_urls_and_dois(text)
        logger.info(f"🔗 URL/DOI extraction: {len(url_refs)} references")
        
        # Strategy 7: Aggressive pattern matching for any citation-like text
        pattern_refs = await self._extract_citation_patterns(text)
        logger.info(f"🎯 Pattern extraction: {len(pattern_refs)} references")
        
        # Combine all references
        all_refs = (section_refs + numbered_refs + author_year_refs + 
                   journal_refs + book_refs + url_refs + pattern_refs)
        
        # Deduplicate and clean
        final_refs = self._deduplicate_and_clean(all_refs)
        
        logger.info(f"✅ Final result: {len(final_refs)} unique references extracted")
        
        return {
            'references': [ref.text for ref in final_refs],
            'detailed_references': [self._reference_to_dict(ref) for ref in final_refs],
            'total_found': len(final_refs),
            'extraction_methods': {
                'section_based': len(section_refs),
                'numbered': len(numbered_refs),
                'author_year': len(author_year_refs),
                'journal': len(journal_refs),
                'book': len(book_refs),
                'url_doi': len(url_refs),
                'pattern': len(pattern_refs)
            },
            'document_name': document_name,
            'confidence_score': self._calculate_overall_confidence(final_refs)
        }
    
    async def _extract_from_reference_sections(self, text: str) -> List[ExtractedReference]:
        """Extract references from dedicated reference sections."""
        references = []
        
        # Find reference sections
        for header_pattern in self.section_headers:
            matches = list(re.finditer(header_pattern, text, re.IGNORECASE | re.MULTILINE))
            
            for match in matches:
                # Extract text after the header
                start_pos = match.end()
                
                # Find the end of the section (next major header or end of document)
                end_patterns = [
                    r'\n\s*(?:appendix|acknowledgments?|figures?|tables?|index)\s*:?\s*\n',
                    r'\n\s*(?:chapter|section)\s+\d+',
                    r'\n\s*[A-Z][A-Z\s]{10,}\s*\n',  # ALL CAPS headers
                ]
                
                end_pos = len(text)
                for end_pattern in end_patterns:
                    end_match = re.search(end_pattern, text[start_pos:], re.IGNORECASE)
                    if end_match:
                        end_pos = start_pos + end_match.start()
                        break
                
                section_text = text[start_pos:end_pos]
                
                # Extract references from this section
                section_refs = await self._process_reference_section(section_text)
                references.extend(section_refs)
        
        return references
    
    async def _process_reference_section(self, section_text: str) -> List[ExtractedReference]:
        """Process a reference section to extract individual references."""
        references = []
        
        # Try numbered patterns first
        for pattern in self.numbered_patterns:
            matches = list(re.finditer(pattern, section_text, re.MULTILINE | re.DOTALL))
            
            for match in matches:
                ref = self._create_reference_from_match(match, "section_numbered")
                if ref and self._is_valid_reference(ref.text):
                    references.append(ref)
        
        # If no numbered references found, try splitting by double newlines
        if not references:
            paragraphs = re.split(r'\n\s*\n', section_text)
            for para in paragraphs:
                para = para.strip()
                if self._looks_like_reference(para):
                    ref = ExtractedReference(
                        text=para,
                        confidence=0.6,
                        extraction_method="section_paragraph",
                        source_section="reference_section"
                    )
                    references.append(ref)
        
        return references

    async def _extract_numbered_references(self, text: str) -> List[ExtractedReference]:
        """Extract numbered references from anywhere in the document."""
        references = []

        for pattern in self.numbered_patterns:
            matches = list(re.finditer(pattern, text, re.MULTILINE | re.DOTALL))

            for match in matches:
                ref = self._create_reference_from_match(match, "numbered_global")
                if ref and self._is_valid_reference(ref.text):
                    references.append(ref)

        return references

    async def _extract_author_year_references(self, text: str) -> List[ExtractedReference]:
        """Extract author-year style references."""
        references = []

        for pattern in self.author_year_patterns:
            matches = list(re.finditer(pattern, text, re.MULTILINE | re.DOTALL))

            for match in matches:
                ref = self._create_reference_from_match(match, "author_year")
                if ref and self._is_valid_reference(ref.text):
                    references.append(ref)

        return references

    async def _extract_journal_articles(self, text: str) -> List[ExtractedReference]:
        """Extract journal article references."""
        references = []

        for pattern in self.journal_patterns:
            matches = list(re.finditer(pattern, text, re.MULTILINE | re.DOTALL))

            for match in matches:
                ref = self._create_reference_from_match(match, "journal")
                if ref and self._is_valid_reference(ref.text):
                    # Extract additional metadata for journal articles
                    groups = match.groups()
                    if len(groups) >= 4:
                        ref.authors = groups[0].strip()
                        ref.title = groups[1].strip()
                        ref.journal = groups[2].strip()
                        ref.year = groups[3].strip()
                    references.append(ref)

        return references

    async def _extract_books(self, text: str) -> List[ExtractedReference]:
        """Extract book references."""
        references = []

        for pattern in self.book_patterns:
            matches = list(re.finditer(pattern, text, re.MULTILINE | re.DOTALL))

            for match in matches:
                ref = self._create_reference_from_match(match, "book")
                if ref and self._is_valid_reference(ref.text):
                    references.append(ref)

        return references

    async def _extract_urls_and_dois(self, text: str) -> List[ExtractedReference]:
        """Extract URLs and DOI references."""
        references = []

        # Extract DOIs and PMIDs
        for pattern in self.identifier_patterns:
            matches = list(re.finditer(pattern, text, re.IGNORECASE))

            for match in matches:
                # Get surrounding context for the reference
                start = max(0, match.start() - 200)
                end = min(len(text), match.end() + 200)
                context = text[start:end].strip()

                ref = ExtractedReference(
                    text=context,
                    confidence=0.8,
                    extraction_method="identifier",
                    source_section="document_body"
                )

                # Extract specific identifiers
                if 'doi' in match.group(0).lower():
                    ref.doi = match.group(1)
                elif 'pmid' in match.group(0).lower():
                    ref.pmid = match.group(1)

                references.append(ref)

        # Extract URLs
        for pattern in self.url_patterns:
            matches = list(re.finditer(pattern, text))

            for match in matches:
                # Get surrounding context
                start = max(0, match.start() - 100)
                end = min(len(text), match.end() + 100)
                context = text[start:end].strip()

                ref = ExtractedReference(
                    text=context,
                    url=match.group(0),
                    confidence=0.7,
                    extraction_method="url",
                    source_section="document_body"
                )
                references.append(ref)

        return references

    async def _extract_citation_patterns(self, text: str) -> List[ExtractedReference]:
        """Extract any text that looks like a citation using aggressive patterns."""
        references = []

        # Very aggressive patterns for citation-like text
        aggressive_patterns = [
            # Any line with author name, year, and journal-like text
            r'[A-Z][a-z]+[^.]*\d{4}[^.]*[A-Z][a-z]{3,}[^.]*\.',

            # Lines with multiple authors and publication info
            r'[A-Z][a-z]+(?:,\s*[A-Z]\.)*(?:,\s*[A-Z][a-z]+)*[^.]*\d{4}[^.]*\.',

            # Text with journal abbreviations
            r'[^.]*\b(?:J|Am J|Eur J|Int J|Proc|Ann|Clin|Med|Sci|Res|Rev)\b[^.]*\d{4}[^.]*\.',

            # Text with volume/page numbers
            r'[^.]*\d{4}[^.]*\d+:\d+[-\d]*[^.]*\.',

            # Text with "et al."
            r'[A-Z][a-z]+\s+et\s+al\.?[^.]*\d{4}[^.]*\.',
        ]

        for pattern in aggressive_patterns:
            matches = list(re.finditer(pattern, text, re.MULTILINE))

            for match in matches:
                ref_text = match.group(0).strip()

                # Additional validation
                if (len(ref_text) > 30 and
                    self._contains_author_indicators(ref_text) and
                    self._contains_publication_indicators(ref_text)):

                    ref = ExtractedReference(
                        text=ref_text,
                        confidence=0.5,
                        extraction_method="aggressive_pattern",
                        source_section="document_body"
                    )
                    references.append(ref)

        return references

    def _create_reference_from_match(self, match: re.Match, method: str) -> Optional[ExtractedReference]:
        """Create a reference object from a regex match."""
        try:
            groups = match.groups()

            if len(groups) >= 2:
                # Numbered reference
                number_str = groups[0]
                ref_text = groups[1].strip()
                number = int(number_str) if number_str.isdigit() else None
            else:
                # Non-numbered reference
                ref_text = match.group(0).strip()
                number = None

            # Clean up the reference text
            ref_text = self._clean_reference_text(ref_text)

            if len(ref_text) < 20:  # Too short to be a real reference
                return None

            return ExtractedReference(
                text=ref_text,
                number=number,
                confidence=0.7,
                extraction_method=method,
                source_section="document"
            )

        except Exception as e:
            logger.warning(f"Error creating reference from match: {e}")
            return None

    def _clean_reference_text(self, text: str) -> str:
        """Clean and normalize reference text."""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove leading numbers and punctuation
        text = re.sub(r'^\d+[\.\)\]\s]+', '', text)

        # Remove leading dashes and brackets
        text = re.sub(r'^[-\[\]\s]+', '', text)

        # Ensure it ends with a period if it looks like a complete reference
        if not text.endswith('.') and len(text) > 50:
            text += '.'

        return text.strip()

    def _is_valid_reference(self, text: str) -> bool:
        """Check if text looks like a valid reference."""
        if len(text) < 20:
            return False

        # Must contain some author-like text
        if not re.search(r'[A-Z][a-z]+', text):
            return False

        # Should contain a year
        if not re.search(r'\b(19|20)\d{2}\b', text):
            return False

        # Should not be just a title or heading
        if text.isupper() or text.count('.') < 1:
            return False

        return True

    def _looks_like_reference(self, text: str) -> bool:
        """Check if text looks like it could be a reference."""
        if len(text) < 15:
            return False

        # Check for author patterns
        author_patterns = [
            r'[A-Z][a-z]+,?\s+[A-Z]\.?',  # Smith, J.
            r'[A-Z][a-z]+\s+et\s+al\.?',  # Smith et al.
            r'[A-Z][a-z]+\s+[A-Z][a-z]+',  # Smith John
        ]

        has_author = any(re.search(pattern, text) for pattern in author_patterns)

        # Check for publication indicators
        pub_indicators = [
            r'\b(19|20)\d{2}\b',  # Year
            r'\b(?:J|Am J|Eur J|Int J|Proc|Ann|Clin|Med|Sci|Res|Rev)\b',  # Journal abbreviations
            r'\d+:\d+',  # Volume:page
            r'doi:',  # DOI
            r'PMID:',  # PubMed ID
        ]

        has_pub_info = any(re.search(pattern, text, re.IGNORECASE) for pattern in pub_indicators)

        return has_author or has_pub_info

    def _contains_author_indicators(self, text: str) -> bool:
        """Check if text contains author-like indicators."""
        author_indicators = [
            r'[A-Z][a-z]+,?\s+[A-Z]\.?',  # Smith, J.
            r'[A-Z][a-z]+\s+et\s+al\.?',  # Smith et al.
            r'[A-Z][a-z]+\s+[A-Z][a-z]+',  # Smith John
            r'[A-Z][a-z]+(?:,\s*[A-Z][a-z]+)+',  # Multiple authors
        ]

        return any(re.search(pattern, text) for pattern in author_indicators)

    def _contains_publication_indicators(self, text: str) -> bool:
        """Check if text contains publication-like indicators."""
        pub_indicators = [
            r'\b(19|20)\d{2}\b',  # Year
            r'\b(?:J|Am J|Eur J|Int J|Proc|Ann|Clin|Med|Sci|Res|Rev)\b',  # Journal abbreviations
            r'\d+:\d+',  # Volume:page
            r'doi:',  # DOI
            r'PMID:',  # PubMed ID
            r'vol\.?\s*\d+',  # Volume
            r'pp\.?\s*\d+',  # Pages
        ]

        return any(re.search(pattern, text, re.IGNORECASE) for pattern in pub_indicators)

    def _deduplicate_and_clean(self, references: List[ExtractedReference]) -> List[ExtractedReference]:
        """Remove duplicates and clean up the reference list."""
        seen_texts = set()
        unique_refs = []

        # Sort by confidence score (highest first)
        references.sort(key=lambda x: x.confidence, reverse=True)

        for ref in references:
            # Normalize text for comparison
            normalized_text = re.sub(r'\s+', ' ', ref.text.lower().strip())

            # Check for duplicates using fuzzy matching
            is_duplicate = False
            for seen_text in seen_texts:
                similarity = self._calculate_text_similarity(normalized_text, seen_text)
                if similarity > 0.8:  # 80% similarity threshold
                    is_duplicate = True
                    break

            if not is_duplicate and len(ref.text) > 20:
                seen_texts.add(normalized_text)
                unique_refs.append(ref)

        return unique_refs

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text strings."""
        # Simple similarity based on common words
        words1 = set(text1.split())
        words2 = set(text2.split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    def _reference_to_dict(self, ref: ExtractedReference) -> Dict[str, Any]:
        """Convert reference object to dictionary."""
        return {
            'text': ref.text,
            'number': ref.number,
            'authors': ref.authors,
            'title': ref.title,
            'journal': ref.journal,
            'year': ref.year,
            'doi': ref.doi,
            'pmid': ref.pmid,
            'url': ref.url,
            'confidence': ref.confidence,
            'extraction_method': ref.extraction_method,
            'source_section': ref.source_section
        }

    def _calculate_overall_confidence(self, references: List[ExtractedReference]) -> float:
        """Calculate overall confidence score for the extraction."""
        if not references:
            return 0.0

        total_confidence = sum(ref.confidence for ref in references)
        avg_confidence = total_confidence / len(references)

        # Boost confidence if we found many references
        count_boost = min(0.2, len(references) * 0.01)

        return min(1.0, avg_confidence + count_boost)

# Global instance
_aggressive_extractor = None

async def get_aggressive_reference_extractor() -> AggressiveReferenceExtractor:
    """Get the global aggressive reference extractor instance."""
    global _aggressive_extractor
    if _aggressive_extractor is None:
        _aggressive_extractor = AggressiveReferenceExtractor()
    return _aggressive_extractor
