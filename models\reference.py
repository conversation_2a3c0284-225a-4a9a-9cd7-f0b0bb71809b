"""
Reference-related data models for the Graphiti application.
"""

from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum
from datetime import datetime

class ExtractionMethod(str, Enum):
    """Reference extraction method enumeration"""
    LLM = "llm"
    REGEX = "regex"
    MANUAL = "manual"

class Reference(BaseModel):
    """Reference model with AI-powered enhancements"""
    source_document: str
    extraction_method: ExtractionMethod
    reference_text: Optional[str] = None
    authors: Optional[str] = None
    title: Optional[str] = None
    year: Optional[str] = None
    journal: Optional[str] = None
    volume: Optional[str] = None
    issue: Optional[str] = None
    pages: Optional[str] = None
    doi: Optional[str] = None
    url: Optional[str] = None
    extraction_date: Optional[datetime] = None
    confidence: Optional[float] = None

    # AI-powered enhancement fields
    reference_id: Optional[str] = None
    quality_score: Optional[float] = None
    ai_enhanced: Optional[bool] = None
    reference_length: Optional[int] = None
    has_doi: Optional[bool] = None
    has_year: Optional[bool] = None
    has_journal: Optional[bool] = None
    normalized_text: Optional[str] = None
    processed_at: Optional[str] = None
    uuid: Optional[str] = None

class ReferenceList(BaseModel):
    """Reference list model"""
    references: List[Reference]
    total: Optional[int] = None
    page: Optional[int] = None
    page_size: Optional[int] = None

class ReferenceExtractionResult(BaseModel):
    """Reference extraction result model"""
    filename: str
    file_path: str
    extraction_date: datetime
    success: bool
    llm_references: List[Dict[str, Any]] = []
    regex_references: List[str] = []
    error: Optional[str] = None

class ReferenceFilter(BaseModel):
    """Reference filter model"""
    source_document: Optional[str] = None
    authors: Optional[str] = None
    year: Optional[str] = None
    journal: Optional[str] = None
    extraction_method: Optional[ExtractionMethod] = None
    search_query: Optional[str] = None

class CitationLink(BaseModel):
    """Citation link model"""
    source_reference_id: str
    target_reference_id: str
    confidence: float = 1.0

class CitationNetwork(BaseModel):
    """Citation network model"""
    references: List[Reference]
    citations: List[CitationLink]
    documents: List[str]
    last_updated: datetime
